// Debug script for keyword operations
// Run this in browser console to test keyword functionality

console.log('🔍 Debugging Keyword Operations');

// Test IndexedDB access
async function testIndexedDB() {
    console.log('📦 Testing IndexedDB access...');
    
    try {
        // Open IndexedDB
        const request = indexedDB.open('vocab-app', 1);
        
        request.onsuccess = function(event) {
            const db = event.target.result;
            console.log('✅ IndexedDB opened successfully');
            console.log('📊 Object stores:', Array.from(db.objectStoreNames));
            
            // Test keyword storage
            const transaction = db.transaction(['keywords'], 'readonly');
            const store = transaction.objectStore('keywords');
            const getAllRequest = store.getAll();
            
            getAllRequest.onsuccess = function() {
                console.log('📝 Current keywords in IndexedDB:', getAllRequest.result);
            };
            
            getAllRequest.onerror = function() {
                console.error('❌ Failed to read keywords from IndexedDB');
            };
        };
        
        request.onerror = function() {
            console.error('❌ Failed to open IndexedDB');
        };
        
    } catch (error) {
        console.error('❌ IndexedDB error:', error);
    }
}

// Test keyword operations
async function testKeywordOperations() {
    console.log('🧪 Testing keyword operations...');
    
    // Check if keywordStorage is available
    if (typeof keywordStorage !== 'undefined') {
        console.log('✅ keywordStorage is available');
        
        try {
            const keywords = await keywordStorage.getKeywords();
            console.log('📝 Current keywords:', keywords);
            
            const selected = await keywordStorage.getSelectedKeywords();
            console.log('✅ Selected keywords:', selected);
            
        } catch (error) {
            console.error('❌ Error accessing keywordStorage:', error);
        }
    } else {
        console.error('❌ keywordStorage is not available');
    }
}

// Test API endpoints
async function testAPIEndpoints() {
    console.log('🌐 Testing API endpoints...');
    
    try {
        // Test GET /api/keywords
        const response = await fetch('/api/keywords');
        if (response.ok) {
            const keywords = await response.json();
            console.log('✅ GET /api/keywords successful:', keywords);
        } else {
            console.error('❌ GET /api/keywords failed:', response.status, response.statusText);
        }
    } catch (error) {
        console.error('❌ API request error:', error);
    }
}

// Run all tests
async function runAllTests() {
    console.log('🚀 Starting keyword debugging...');
    
    await testIndexedDB();
    await testKeywordOperations();
    await testAPIEndpoints();
    
    console.log('✅ Debugging complete!');
}

// Auto-run tests
runAllTests();

// Export functions for manual testing
window.debugKeywords = {
    testIndexedDB,
    testKeywordOperations,
    testAPIEndpoints,
    runAllTests
};

console.log('💡 Use window.debugKeywords to run individual tests');
