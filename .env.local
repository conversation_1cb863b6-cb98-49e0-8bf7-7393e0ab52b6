# Database
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/vocab?schema=public&connection_limit=5&pool_timeout=10"

# Database Configuration
DB_CONNECT_TIMEOUT=10000
DB_QUERY_TIMEOUT=30000
DB_POOL_TIMEOUT=10000

# JWT Configuration
JWT_COOKIE_NAME=auth_token
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=2592000

# LLM Configuration
LLM_OPENAI_API_KEY=your_openai_api_key_here

# Client-side Configuration (Public)
NEXT_PUBLIC_JWT_COOKIE_NAME=auth_token

# Admin Dashboard
ADMIN_DASHBOARD_PASSWORD=admin123
