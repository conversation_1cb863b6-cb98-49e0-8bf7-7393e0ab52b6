'use client';

import { useKeywordsContext } from '@/contexts';
import { Button, Card, Input } from '@/components/ui';
import { useState, useEffect } from 'react';
import { Check, Clock, AlertCircle } from 'lucide-react';

function TestSyncContent() {
	const { keywords, selectedKeywords, setSelectedKeywords, createKeyword, deleteKeyword } =
		useKeywordsContext();

	const [newKeywordName, setNewKeywordName] = useState('');

	const handleCreateKeyword = async () => {
		if (!newKeywordName.trim()) return;
		await createKeyword(newKeywordName.trim());
		setNewKeywordName('');
	};

	const handleDeleteKeyword = async (id: string) => {
		await deleteKeyword(id);
	};

	const toggleSelection = (id: string) => {
		if (selectedKeywords.includes(id)) {
			setSelectedKeywords(selectedKeywords.filter((kId) => kId !== id));
		} else {
			setSelectedKeywords([...selectedKeywords, id]);
		}
	};

	// Note: Sync status removed - now using direct server API calls

	return (
		<div className="container mx-auto p-6 space-y-6">
			<h1 className="text-3xl font-bold">Keyword Management Test</h1>
			<p className="text-gray-600">
				Test keyword management with direct server API calls. No sync status needed.
			</p>

			{/* Status Card */}
			<Card className="p-4 border-2 text-green-600 bg-green-50 border-green-200">
				<div className="flex items-center gap-3">
					<Check className="w-4 h-4 text-green-600" />
					<div>
						<h3 className="font-semibold">Direct Server API</h3>
						<p className="text-sm opacity-75">
							All operations communicate directly with server
						</p>
					</div>
				</div>
			</Card>

			{/* Create Keyword */}
			<Card className="p-4">
				<h2 className="text-xl font-semibold mb-3">Create Keyword</h2>
				<div className="flex gap-2">
					<Input
						value={newKeywordName}
						onChange={(e) => setNewKeywordName(e.target.value)}
						placeholder="Enter keyword name"
						onKeyDown={(e) => {
							if (e.key === 'Enter') {
								handleCreateKeyword();
							}
						}}
					/>
					<Button onClick={handleCreateKeyword} disabled={!newKeywordName.trim()}>
						Create
					</Button>
				</div>
				<p className="text-sm text-gray-500 mt-2">
					Watch the sync status change when you create a keyword!
				</p>
			</Card>

			{/* Keywords List */}
			<Card className="p-4">
				<h2 className="text-xl font-semibold mb-3">Keywords ({keywords.length})</h2>
				<div className="space-y-2">
					{keywords.map((keyword) => (
						<div
							key={keyword.id}
							className={`p-3 border rounded-lg ${
								selectedKeywords.includes(keyword.id)
									? 'bg-blue-50 border-blue-300'
									: 'bg-gray-50 border-gray-300'
							}`}
						>
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-2">
									<input
										type="checkbox"
										checked={selectedKeywords.includes(keyword.id)}
										onChange={() => toggleSelection(keyword.id)}
									/>
									<span className="font-medium">{keyword.content}</span>
								</div>
								<Button
									onClick={() => handleDeleteKeyword(keyword.id)}
									variant="destructive"
									size="sm"
								>
									Delete
								</Button>
							</div>
							<div className="text-xs text-gray-500 mt-1">
								ID: {keyword.id.slice(0, 8)}...
							</div>
						</div>
					))}
					{keywords.length === 0 && (
						<p className="text-gray-500 text-center py-4">
							No keywords found. Create one to test sync status!
						</p>
					)}
				</div>
			</Card>

			{/* Instructions */}
			<Card className="p-4 bg-blue-50 border-blue-200">
				<h3 className="font-semibold text-blue-800 mb-2">How to Test:</h3>
				<ol className="list-decimal list-inside space-y-1 text-blue-700">
					<li>Create a keyword - watch the sync status show &quot;pending&quot;</li>
					<li>
						Wait 2-5 seconds - see it sync and show &quot;Synced successfully!&quot;
					</li>
					<li>After 2 seconds - the success message disappears</li>
					<li>Delete a keyword - same process repeats</li>
					<li>Create multiple keywords quickly - see pending count increase</li>
				</ol>
			</Card>
		</div>
	);
}

export default function TestSyncPage() {
	const [mounted, setMounted] = useState(false);

	useEffect(() => {
		setMounted(true);
	}, []);

	if (!mounted) {
		return (
			<div className="container mx-auto p-6">
				<h1 className="text-3xl font-bold">Loading...</h1>
			</div>
		);
	}

	return <TestSyncContent />;
}
